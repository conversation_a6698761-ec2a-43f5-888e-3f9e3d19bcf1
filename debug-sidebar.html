<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Sidebar Extension</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .debug-section {
            background: #f5f5f5;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #007cba;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        #output {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>🔧 Sidebar Extension Debug Tool</h1>
    
    <div class="debug-section">
        <h2>Extension Status</h2>
        <p>This page helps debug the Smart Autofill Assistant sidebar extension.</p>
        <button onclick="checkExtensionStatus()">Check Extension Status</button>
        <button onclick="testSidebarAPI()">Test Sidebar API</button>
        <button onclick="clearOutput()">Clear Output</button>
    </div>

    <div class="debug-section">
        <h2>Instructions</h2>
        <ol>
            <li>Make sure the Smart Autofill Assistant extension is loaded</li>
            <li>Click "Check Extension Status" to verify the extension is working</li>
            <li>Click the extension icon in the Chrome toolbar</li>
            <li>The sidebar should appear on the right side of the browser</li>
        </ol>
    </div>

    <div class="debug-section">
        <h2>Debug Output</h2>
        <div id="output">Ready for debugging...\n</div>
    </div>

    <script>
        function log(message, type = 'info') {
            const output = document.getElementById('output');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
            output.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            output.scrollTop = output.scrollHeight;
        }

        function clearOutput() {
            document.getElementById('output').textContent = 'Output cleared...\n';
        }

        async function checkExtensionStatus() {
            log('Checking extension status...');
            
            try {
                // Check if chrome.runtime is available
                if (typeof chrome === 'undefined' || !chrome.runtime) {
                    log('Chrome extension API not available', 'error');
                    return;
                }
                
                log('Chrome extension API is available', 'success');
                
                // Try to get extension info
                const manifest = chrome.runtime.getManifest();
                log(`Extension: ${manifest.name} v${manifest.version}`, 'success');
                
                // Check permissions
                if (manifest.permissions.includes('sidePanel')) {
                    log('sidePanel permission granted', 'success');
                } else {
                    log('sidePanel permission missing', 'error');
                }
                
                // Check if side_panel is configured in manifest
                if (manifest.side_panel) {
                    log(`Side panel configured: ${manifest.side_panel.default_path}`, 'success');
                } else {
                    log('Side panel not configured in manifest', 'error');
                }
                
            } catch (error) {
                log(`Error checking extension: ${error.message}`, 'error');
            }
        }

        async function testSidebarAPI() {
            log('Testing sidebar API...');
            
            try {
                if (!chrome.sidePanel) {
                    log('chrome.sidePanel API not available (Chrome 114+ required)', 'error');
                    return;
                }
                
                log('chrome.sidePanel API is available', 'success');
                
                // Get current panel behavior
                const behavior = await chrome.sidePanel.getPanelBehavior();
                log(`Panel behavior: openPanelOnActionClick = ${behavior.openPanelOnActionClick}`, 'info');
                
                // Get current panel options
                const options = await chrome.sidePanel.getOptions({});
                log(`Panel options: enabled = ${options.enabled}, path = ${options.path}`, 'info');
                
                log('Sidebar API test completed', 'success');
                
            } catch (error) {
                log(`Error testing sidebar API: ${error.message}`, 'error');
            }
        }

        // Auto-run basic checks when page loads
        window.addEventListener('load', () => {
            log('Debug page loaded');
            setTimeout(checkExtensionStatus, 1000);
        });
    </script>
</body>
</html>
