// src/popup/popup.js
// Main popup interface controller
// Handles UI interactions, view management, and communication with background scripts

import { ProfileService } from '../shared/services/profile-service.js'
import { StorageUtils } from '../shared/utils/storage-utils.js'
import { ValidationUtils } from '../shared/utils/validation-utils.js'
import { FormatUtils } from '../shared/utils/format-utils.js'

class PopupController {
  constructor () {
    this.currentView = 'profileView'
    this.selectedProfile = null
    this.editingProfile = null
    this.settings = null

    this.init()
  }

  async init () {
    try {
      await this.loadSettings()
      await this.loadProfiles()
      this.setupEventListeners()
      this.showView('profileView')
    } catch (error) {
      console.error('Failed to initialize popup:', error)
      this.showError('Failed to load extension data')
    }
  }

  async loadSettings () {
    try {
      this.settings = await StorageUtils.get('autofill_settings', {
        apiKey: '',
        modelConfig: {
          temperature: 0.1,
          maxOutputTokens: 1000,
          thinkingBudget: 0
        },
        uiConfig: {
          autoAnalyze: true,
          showConfirmation: true
        }
      })

      this.updateSettingsForm()
    } catch (error) {
      console.error('Failed to load settings:', error)
    }
  }

  async loadProfiles () {
    try {
      const profiles = await ProfileService.getAllProfiles()
      this.renderProfiles(profiles)

      // Update UI state
      const hasProfiles = profiles.length > 0
      document.getElementById('emptyState').classList.toggle('d-none', hasProfiles)
      document.getElementById('profileList').classList.toggle('d-none', !hasProfiles)
      document.getElementById('analyzePageBtn').disabled = !hasProfiles || !this.selectedProfile
    } catch (error) {
      console.error('Failed to load profiles:', error)
      this.showError('Failed to load profiles')
    }
  }

  renderProfiles (profiles) {
    const container = document.getElementById('profileList')
    container.innerHTML = ''

    profiles.forEach(profile => {
      const profileElement = this.createProfileElement(profile)
      container.appendChild(profileElement)
    })
  }

  createProfileElement (profile) {
    const element = document.createElement('div')
    element.className = 'profile-item'
    element.dataset.profileId = profile.id

    element.innerHTML = `
      <div class="profile-item-header">
        <h3 class="profile-name">${FormatUtils.escapeHtml(profile.name)}</h3>
        <div class="profile-actions">
          <button class="btn btn-icon btn-small edit-profile" title="Edit">
            <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
              <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
            </svg>
          </button>
          <button class="btn btn-icon btn-small delete-profile" title="Delete">
            <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <polyline points="3,6 5,6 21,6"></polyline>
              <path d="M19,6v14a2,2,0,0,1-2,2H7a2,2,0,0,1-2-2V6m3,0V4a2,2,0,0,1,2-2h4a2,2,0,0,1,2,2v2"></path>
            </svg>
          </button>
        </div>
      </div>
      <p class="profile-info">${FormatUtils.truncateText(FormatUtils.escapeHtml(profile.info), 120)}</p>
      <div class="profile-meta">
        <span>Updated ${FormatUtils.formatRelativeTime(profile.updatedAt)}</span>
        <span class="badge badge-info">${profile.info.length} chars</span>
      </div>
    `

    // Add click handler for selection
    element.addEventListener('click', (e) => {
      if (!e.target.closest('.profile-actions')) {
        this.selectProfile(profile.id)
      }
    })

    // Add edit handler
    element.querySelector('.edit-profile').addEventListener('click', (e) => {
      e.stopPropagation()
      this.editProfile(profile.id)
    })

    // Add delete handler
    element.querySelector('.delete-profile').addEventListener('click', (e) => {
      e.stopPropagation()
      this.deleteProfile(profile.id)
    })

    return element
  }

  selectProfile (profileId) {
    // Remove previous selection
    document.querySelectorAll('.profile-item.selected').forEach(item => {
      item.classList.remove('selected')
    })

    // Add selection to clicked item
    const profileElement = document.querySelector(`[data-profile-id="${profileId}"]`)
    if (profileElement) {
      profileElement.classList.add('selected')
      this.selectedProfile = profileId
      document.getElementById('analyzePageBtn').disabled = false
    }
  }

  async editProfile (profileId) {
    try {
      const profile = await ProfileService.getProfile(profileId)
      this.editingProfile = profile

      // Populate form
      document.getElementById('profileName').value = profile.name
      document.getElementById('profileInfo').value = profile.info
      document.getElementById('editorTitle').textContent = 'Edit Profile'

      this.updateCharCount()
      this.showView('editorView')
    } catch (error) {
      console.error('Failed to load profile for editing:', error)
      this.showError('Failed to load profile')
    }
  }

  async deleteProfile (profileId) {
    if (!confirm('Are you sure you want to delete this profile?')) {
      return
    }

    try {
      await ProfileService.deleteProfile(profileId)
      await this.loadProfiles()

      // Clear selection if deleted profile was selected
      if (this.selectedProfile === profileId) {
        this.selectedProfile = null
        document.getElementById('analyzePageBtn').disabled = true
      }

      this.showSuccess('Profile deleted successfully')
    } catch (error) {
      console.error('Failed to delete profile:', error)
      this.showError('Failed to delete profile')
    }
  }

  setupEventListeners () {
    // Navigation
    document.getElementById('settingsBtn').addEventListener('click', () => {
      this.showView('settingsView')
    })

    document.getElementById('backBtn').addEventListener('click', () => {
      this.showView('profileView')
    })

    document.getElementById('settingsBackBtn').addEventListener('click', () => {
      this.showView('profileView')
    })

    // Profile actions
    document.getElementById('addProfileBtn').addEventListener('click', () => {
      this.addNewProfile()
    })

    document.getElementById('createFirstProfileBtn').addEventListener('click', () => {
      this.addNewProfile()
    })

    document.getElementById('analyzePageBtn').addEventListener('click', () => {
      this.performAutofill()
    })

    // Profile form
    document.getElementById('profileForm').addEventListener('submit', (e) => {
      this.handleProfileSubmit(e)
    })

    document.getElementById('cancelBtn').addEventListener('click', () => {
      this.showView('profileView')
    })

    document.getElementById('profileInfo').addEventListener('input', () => {
      this.updateCharCount()
    })

    // Settings
    document.getElementById('saveSettingsBtn').addEventListener('click', () => {
      this.saveSettings()
    })

    document.getElementById('resetSettingsBtn').addEventListener('click', () => {
      this.resetSettings()
    })
  }

  addNewProfile () {
    this.editingProfile = null
    document.getElementById('profileForm').reset()
    document.getElementById('editorTitle').textContent = 'Add Profile'
    this.updateCharCount()
    this.showView('editorView')
  }

  async handleProfileSubmit (e) {
    e.preventDefault()

    const nameInput = document.getElementById('profileName')
    const infoInput = document.getElementById('profileInfo')
    const saveBtn = document.getElementById('saveBtn')

    // Validate inputs
    const nameValidation = ValidationUtils.validateProfileName(nameInput.value)
    const infoValidation = ValidationUtils.validateProfileInfo(infoInput.value)

    if (!nameValidation.isValid) {
      this.showError(nameValidation.errors[0])
      nameInput.focus()
      return
    }

    if (!infoValidation.isValid) {
      this.showError(infoValidation.errors[0])
      infoInput.focus()
      return
    }

    // Show loading state
    saveBtn.classList.add('loading')
    saveBtn.disabled = true

    try {
      if (this.editingProfile) {
        // Update existing profile
        await ProfileService.updateProfile(this.editingProfile.id, {
          name: nameValidation.sanitized,
          info: infoValidation.sanitized
        })
        this.showSuccess('Profile updated successfully')
      } else {
        // Create new profile
        const newProfile = await ProfileService.createProfile(
          nameValidation.sanitized,
          infoValidation.sanitized
        )
        this.selectedProfile = newProfile.id
        this.showSuccess('Profile created successfully')
      }

      await this.loadProfiles()
      this.showView('profileView')
    } catch (error) {
      console.error('Failed to save profile:', error)
      this.showError('Failed to save profile')
    } finally {
      saveBtn.classList.remove('loading')
      saveBtn.disabled = false
    }
  }

  updateCharCount () {
    const textarea = document.getElementById('profileInfo')
    const counter = document.getElementById('charCount')
    const length = textarea.value.length
    counter.textContent = `${length} / 2000 characters`

    if (length > 1800) {
      counter.classList.add('text-warning')
    } else {
      counter.classList.remove('text-warning')
    }
  }

  async performAutofill () {
    if (!this.selectedProfile) {
      this.showError('Please select a profile first')
      return
    }

    if (!this.settings.apiKey) {
      this.showError('Please configure your API key in settings')
      this.showView('settingsView')
      return
    }

    try {
      console.log('Starting autofill process from popup...')
      this.showView('statusView')

      // Get the selected profile
      console.log('Getting profile:', this.selectedProfile)
      const profile = await ProfileService.getProfile(this.selectedProfile)
      console.log('Profile retrieved:', profile)

      // Get current tab
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true })
      console.log('Current tab:', tab)

      if (!tab) {
        throw new Error('No active tab found')
      }

      // Check if tab URL is valid for content script injection
      if (tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://') ||
          tab.url.startsWith('moz-extension://') || tab.url.startsWith('about:')) {
        this.showError('Cannot autofill on system pages. Please navigate to a regular webpage with forms.')
        return
      }

      console.log('Sending autofill message to content script...')

      // Send message to content script to perform autofill
      const response = await chrome.tabs.sendMessage(tab.id, {
        action: 'performAutofill',
        profileInfo: profile.info,
        settings: this.settings
      })

      console.log('Content script response:', response)

      if (response && response.success) {
        this.showAutofillResults(response.results)
      } else {
        const errorMessage = response?.error || 'Autofill failed - no response from content script'
        console.error('Autofill failed:', errorMessage)
        this.showError(errorMessage)
      }
    } catch (error) {
      console.error('Autofill failed with error:', error)
      console.error('Error stack:', error.stack)

      let errorMessage = 'Failed to perform autofill'

      if (error.message.includes('Could not establish connection')) {
        errorMessage = 'Could not connect to the page. Please refresh the page and try again.'
      } else if (error.message.includes('No active tab')) {
        errorMessage = 'No active tab found. Please make sure you have a webpage open.'
      } else if (error.message.includes('system pages')) {
        errorMessage = error.message
      } else if (error.message.includes('Profile not found')) {
        errorMessage = 'Selected profile not found. Please select a different profile.'
      }

      this.showError(errorMessage)
    }
  }

  showAutofillResults (results) {
    const statusMessage = document.getElementById('statusMessage')
    const statusResults = document.getElementById('statusResults')

    statusMessage.classList.add('d-none')
    statusResults.classList.remove('d-none')

    const { filled, failed, skipped } = results

    statusResults.innerHTML = `
      <div class="status-summary mb-lg">
        <h3>Autofill Complete</h3>
        <p>${filled.length} fields filled, ${failed.length} failed, ${skipped.length} skipped</p>
      </div>

      ${filled.length > 0
? `
        <div class="result-section">
          <h4 class="text-success mb-md">✓ Successfully Filled (${filled.length})</h4>
          ${filled.map(item => `
            <div class="result-item">
              <div class="result-field">${item.selector}</div>
              <div class="result-value">${FormatUtils.truncateText(item.value, 50)}</div>
            </div>
          `).join('')}
        </div>
      `
: ''}

      ${failed.length > 0
? `
        <div class="result-section">
          <h4 class="text-error mb-md">✗ Failed (${failed.length})</h4>
          ${failed.map(item => `
            <div class="result-item failed">
              <div class="result-field">${item.selector}</div>
              <div class="result-value">${item.reason}</div>
            </div>
          `).join('')}
        </div>
      `
: ''}

      <div class="form-actions mt-lg">
        <button id="backToProfilesBtn" class="btn btn-primary w-full">
          Back to Profiles
        </button>
      </div>
    `

    document.getElementById('backToProfilesBtn').addEventListener('click', () => {
      this.showView('profileView')
    })
  }

  updateSettingsForm () {
    if (!this.settings) return

    document.getElementById('apiKey').value = this.settings.apiKey || ''
    document.getElementById('temperature').value = this.settings.modelConfig.temperature || 0.1
    document.getElementById('maxTokens').value = this.settings.modelConfig.maxOutputTokens || 1000
    document.getElementById('autoAnalyze').checked = this.settings.uiConfig.autoAnalyze !== false
  }

  async saveSettings () {
    const apiKey = document.getElementById('apiKey').value.trim()
    const temperature = parseFloat(document.getElementById('temperature').value)
    const maxTokens = parseInt(document.getElementById('maxTokens').value)
    const autoAnalyze = document.getElementById('autoAnalyze').checked

    // Validate settings
    const apiKeyValidation = ValidationUtils.validateApiKey(apiKey)
    const tempValidation = ValidationUtils.validateTemperature(temperature)
    const tokensValidation = ValidationUtils.validateMaxTokens(maxTokens)

    if (!apiKeyValidation.isValid) {
      this.showError(apiKeyValidation.errors[0])
      return
    }

    if (!tempValidation.isValid) {
      this.showError(tempValidation.errors[0])
      return
    }

    if (!tokensValidation.isValid) {
      this.showError(tokensValidation.errors[0])
      return
    }

    try {
      this.settings = {
        apiKey: apiKeyValidation.sanitized,
        modelConfig: {
          temperature: tempValidation.value,
          maxOutputTokens: tokensValidation.value,
          thinkingBudget: 0
        },
        uiConfig: {
          autoAnalyze,
          showConfirmation: true
        }
      }

      await StorageUtils.set('autofill_settings', this.settings)
      this.showSuccess('Settings saved successfully')
    } catch (error) {
      console.error('Failed to save settings:', error)
      this.showError('Failed to save settings')
    }
  }

  resetSettings () {
    if (confirm('Reset all settings to defaults?')) {
      this.settings = {
        apiKey: '',
        modelConfig: {
          temperature: 0.1,
          maxOutputTokens: 1000,
          thinkingBudget: 0
        },
        uiConfig: {
          autoAnalyze: true,
          showConfirmation: true
        }
      }

      this.updateSettingsForm()
      this.showSuccess('Settings reset to defaults')
    }
  }

  showView (viewId) {
    // Hide all views
    document.querySelectorAll('.view').forEach(view => {
      view.classList.remove('active')
    })

    // Show target view
    document.getElementById(viewId).classList.add('active')
    this.currentView = viewId

    // Reset status view when switching away
    if (viewId !== 'statusView') {
      document.getElementById('statusMessage').classList.remove('d-none')
      document.getElementById('statusResults').classList.add('d-none')
    }
  }

  showError (message) {
    // Simple error display - could be enhanced with toast notifications
    console.error(message)
    alert(message)
  }

  showSuccess (message) {
    // Simple success display - could be enhanced with toast notifications
    console.log(message)
  }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  const popupController = new PopupController()
  window.popupController = popupController // For debugging
})
