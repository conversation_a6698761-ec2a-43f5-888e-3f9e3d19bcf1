<!-- location/src/sidebar/sidebar.html -->
<!-- Sidebar interface for Smart Autofill Assistant browser extension -->
<!-- Provides a sidebar popup interface for form autofill functionality instead of modal popup -->

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Smart Autofill Assistant</title>
  <link rel="stylesheet" href="sidebar.css">
</head>
<body>
  <div class="sidebar-container">
    <!-- Header -->
    <header class="sidebar-header">
      <div class="header-content">
        <h1 class="app-title">
          <span class="app-icon">🤖</span>
          Smart Autofill
        </h1>
        <div class="header-actions">
          <button id="settingsBtn" class="icon-btn" title="Settings">
            <span class="icon">⚙️</span>
          </button>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="sidebar-main">
      <!-- Profile Selection View -->
      <div id="profileView" class="view active">
        <div class="section">
          <h2 class="section-title">Select Profile</h2>
          <div class="profile-list" id="profileList">
            <!-- Profiles will be populated here -->
          </div>
          <div class="profile-actions">
            <button id="createProfileBtn" class="btn btn-secondary">
              <span class="icon">➕</span>
              Create New Profile
            </button>
          </div>
        </div>

        <div class="section">
          <h2 class="section-title">Quick Actions</h2>
          <div class="action-buttons">
            <button id="analyzeBtn" class="btn btn-outline">
              <span class="icon">🔍</span>
              Analyze Page
            </button>
            <button id="autofillBtn" class="btn btn-primary" disabled>
              <span class="icon">✨</span>
              Autofill Form
            </button>
          </div>
        </div>
      </div>

      <!-- Settings View -->
      <div id="settingsView" class="view">
        <div class="view-header">
          <button id="backToProfileBtn" class="btn btn-ghost">
            <span class="icon">←</span>
            Back
          </button>
          <h2 class="view-title">Settings</h2>
        </div>

        <div class="section">
          <h3 class="section-title">API Configuration</h3>
          <div class="form-group">
            <label for="apiKeyInput" class="form-label">Gemini API Key</label>
            <input type="password" id="apiKeyInput" class="form-input"
                   placeholder="Enter your Gemini API key">
            <div class="form-help">
              Get your API key from
              <a href="https://makersuite.google.com/app/apikey" target="_blank">Google AI Studio</a>
            </div>
          </div>
          <div class="form-group">
            <button id="saveSettingsBtn" class="btn btn-primary">Save Settings</button>
          </div>
        </div>

        <div class="section">
          <h3 class="section-title">Autofill Preferences</h3>
          <div class="form-group">
            <label class="checkbox-label">
              <input type="checkbox" id="autoAnalyzeCheckbox">
              <span class="checkbox-custom"></span>
              Auto-analyze forms on page load
            </label>
          </div>
          <div class="form-group">
            <label class="checkbox-label">
              <input type="checkbox" id="showPreviewCheckbox" checked>
              <span class="checkbox-custom"></span>
              Show preview before autofill
            </label>
          </div>
        </div>
      </div>

      <!-- Profile Creation View -->
      <div id="createProfileView" class="view">
        <div class="view-header">
          <button id="backFromCreateBtn" class="btn btn-ghost">
            <span class="icon">←</span>
            Back
          </button>
          <h2 class="view-title">Create Profile</h2>
        </div>

        <div class="section">
          <div class="form-group">
            <label for="profileNameInput" class="form-label">Profile Name</label>
            <input type="text" id="profileNameInput" class="form-input"
                   placeholder="e.g., Personal, Work, etc.">
          </div>
        </div>

        <div class="section">
          <h3 class="section-title">Personal Information</h3>
          <div class="form-grid">
            <div class="form-group">
              <label for="firstNameInput" class="form-label">First Name</label>
              <input type="text" id="firstNameInput" class="form-input">
            </div>
            <div class="form-group">
              <label for="lastNameInput" class="form-label">Last Name</label>
              <input type="text" id="lastNameInput" class="form-input">
            </div>
            <div class="form-group">
              <label for="emailInput" class="form-label">Email</label>
              <input type="email" id="emailInput" class="form-input">
            </div>
            <div class="form-group">
              <label for="phoneInput" class="form-label">Phone</label>
              <input type="tel" id="phoneInput" class="form-input">
            </div>
          </div>
        </div>

        <div class="section">
          <h3 class="section-title">Address</h3>
          <div class="form-grid">
            <div class="form-group form-group-full">
              <label for="addressLine1Input" class="form-label">Address Line 1</label>
              <input type="text" id="addressLine1Input" class="form-input">
            </div>
            <div class="form-group form-group-full">
              <label for="addressLine2Input" class="form-label">Address Line 2</label>
              <input type="text" id="addressLine2Input" class="form-input">
            </div>
            <div class="form-group">
              <label for="cityInput" class="form-label">City</label>
              <input type="text" id="cityInput" class="form-input">
            </div>
            <div class="form-group">
              <label for="stateInput" class="form-label">State</label>
              <input type="text" id="stateInput" class="form-input">
            </div>
            <div class="form-group">
              <label for="postalCodeInput" class="form-label">Postal Code</label>
              <input type="text" id="postalCodeInput" class="form-input">
            </div>
            <div class="form-group">
              <label for="countryInput" class="form-label">Country</label>
              <input type="text" id="countryInput" class="form-input">
            </div>
          </div>
        </div>

        <div class="section">
          <div class="form-actions">
            <button id="saveProfileBtn" class="btn btn-primary">Create Profile</button>
            <button id="cancelCreateBtn" class="btn btn-secondary">Cancel</button>
          </div>
        </div>
      </div>

      <!-- Status View -->
      <div id="statusView" class="view">
        <div class="status-content">
          <div class="status-icon">
            <div class="spinner"></div>
          </div>
          <h3 class="status-title">Processing...</h3>
          <p class="status-message" id="statusMessage">Analyzing form structure...</p>
        </div>
      </div>

      <!-- Results View -->
      <div id="resultsView" class="view">
        <div class="view-header">
          <button id="backFromResultsBtn" class="btn btn-ghost">
            <span class="icon">←</span>
            Back
          </button>
          <h2 class="view-title">Autofill Results</h2>
        </div>

        <div class="section">
          <div id="resultsContent">
            <!-- Results will be populated here -->
          </div>
        </div>
      </div>
    </main>

    <!-- Footer -->
    <footer class="sidebar-footer">
      <div class="footer-content">
        <div class="status-indicator" id="statusIndicator">
          <span class="status-dot"></span>
          <span class="status-text">Ready</span>
        </div>
      </div>
    </footer>
  </div>

  <script type="module" src="sidebar.js"></script>
</body>
</html>
