<!-- location/src/sidebar/sidebar-test.html -->
<!-- Simple test version of sidebar to verify basic functionality -->
<!-- Minimal sidebar interface for debugging sidebar display issues -->

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Sidebar Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background: #f0f0f0;
      width: 100%;
      min-height: 100vh;
    }
    .container {
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    h1 {
      color: #333;
      margin-top: 0;
    }
    .status {
      padding: 10px;
      margin: 10px 0;
      border-radius: 4px;
      background: #e7f3ff;
      border-left: 4px solid #007cba;
    }
    button {
      background: #007cba;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      margin: 5px 0;
      display: block;
      width: 100%;
    }
    button:hover {
      background: #005a87;
    }
    #output {
      background: #f8f9fa;
      border: 1px solid #dee2e6;
      padding: 10px;
      margin: 10px 0;
      border-radius: 4px;
      font-family: monospace;
      font-size: 12px;
      max-height: 200px;
      overflow-y: auto;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>🔧 Sidebar Test</h1>
    
    <div class="status">
      <strong>Status:</strong> Sidebar is working! 🎉
    </div>
    
    <p>If you can see this page, the sidebar is displaying correctly.</p>
    
    <button onclick="testExtensionAPI()">Test Extension API</button>
    <button onclick="testBackgroundMessage()">Test Background Message</button>
    <button onclick="clearOutput()">Clear Output</button>
    
    <div id="output">
      Ready for testing...<br>
    </div>
  </div>

  <script>
    function log(message) {
      const output = document.getElementById('output');
      const timestamp = new Date().toLocaleTimeString();
      output.innerHTML += `[${timestamp}] ${message}<br>`;
      output.scrollTop = output.scrollHeight;
    }

    function clearOutput() {
      document.getElementById('output').innerHTML = 'Output cleared...<br>';
    }

    async function testExtensionAPI() {
      log('Testing extension API...');
      
      try {
        if (typeof chrome === 'undefined') {
          log('❌ Chrome API not available');
          return;
        }
        
        log('✅ Chrome API available');
        
        if (chrome.runtime) {
          log('✅ chrome.runtime available');
          const manifest = chrome.runtime.getManifest();
          log(`✅ Extension: ${manifest.name} v${manifest.version}`);
        }
        
        if (chrome.sidePanel) {
          log('✅ chrome.sidePanel API available');
          try {
            const behavior = await chrome.sidePanel.getPanelBehavior();
            log(`✅ Panel behavior: openPanelOnActionClick = ${behavior.openPanelOnActionClick}`);
          } catch (error) {
            log(`⚠️ Could not get panel behavior: ${error.message}`);
          }
        } else {
          log('❌ chrome.sidePanel API not available');
        }
        
      } catch (error) {
        log(`❌ Error: ${error.message}`);
      }
    }

    async function testBackgroundMessage() {
      log('Testing background message...');
      
      try {
        const response = await chrome.runtime.sendMessage({ action: 'getSettings' });
        if (response && response.success) {
          log('✅ Background communication working');
          log(`✅ Settings loaded: ${Object.keys(response.data).length} keys`);
        } else {
          log('❌ Background communication failed');
        }
      } catch (error) {
        log(`❌ Message error: ${error.message}`);
      }
    }

    // Auto-run tests when page loads
    window.addEventListener('load', () => {
      log('Sidebar test page loaded');
      setTimeout(testExtensionAPI, 500);
    });
  </script>
</body>
</html>
