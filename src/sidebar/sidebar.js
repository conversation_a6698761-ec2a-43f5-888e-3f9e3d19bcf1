// location/src/sidebar/sidebar.js
// Sidebar interface controller for Smart Autofill Assistant browser extension
// Manages the sidebar popup interface, user interactions, and communication with background/content scripts

class SidebarController {
  constructor () {
    this.selectedProfile = null
    this.settings = {}
    this.profiles = []
    this.currentView = 'profileView'

    this.init()
  }

  async init () {
    console.log('Initializing sidebar controller...')

    // Load initial data
    await this.loadSettings()
    await this.loadProfiles()

    // Setup event listeners
    this.setupEventListeners()

    // Initialize UI
    this.updateUI()

    console.log('Sidebar controller initialized')
  }

  setupEventListeners () {
    // Navigation
    document.getElementById('settingsBtn').addEventListener('click', () => {
      this.showView('settingsView')
    })

    document.getElementById('backToProfileBtn').addEventListener('click', () => {
      this.showView('profileView')
    })

    document.getElementById('createProfileBtn').addEventListener('click', () => {
      this.showView('createProfileView')
    })

    document.getElementById('backFromCreateBtn').addEventListener('click', () => {
      this.showView('profileView')
    })

    document.getElementById('backFromResultsBtn').addEventListener('click', () => {
      this.showView('profileView')
    })

    // Actions
    document.getElementById('analyzeBtn').addEventListener('click', () => {
      this.analyzePage()
    })

    document.getElementById('autofillBtn').addEventListener('click', () => {
      this.performAutofill()
    })

    // Settings
    document.getElementById('saveSettingsBtn').addEventListener('click', () => {
      this.saveSettings()
    })

    // Profile creation
    document.getElementById('saveProfileBtn').addEventListener('click', () => {
      this.saveProfile()
    })

    document.getElementById('cancelCreateBtn').addEventListener('click', () => {
      this.showView('profileView')
    })
  }

  showView (viewId) {
    // Hide all views
    document.querySelectorAll('.view').forEach(view => {
      view.classList.remove('active')
    })

    // Show target view
    document.getElementById(viewId).classList.add('active')
    this.currentView = viewId
  }

  async loadSettings () {
    try {
      const response = await chrome.runtime.sendMessage({ action: 'getSettings' })
      if (response.success) {
        this.settings = response.data
        this.updateSettingsUI()
      }
    } catch (error) {
      console.error('Failed to load settings:', error)
    }
  }

  async loadProfiles () {
    try {
      // Import ProfileService dynamically
      const { ProfileService } = await import('../shared/services/profile-service.js')
      this.profiles = await ProfileService.getAllProfiles()
      this.updateProfileList()
    } catch (error) {
      console.error('Failed to load profiles:', error)
    }
  }

  updateSettingsUI () {
    const apiKeyInput = document.getElementById('apiKeyInput')
    if (apiKeyInput && this.settings.apiKey) {
      apiKeyInput.value = this.settings.apiKey
    }

    const autoAnalyzeCheckbox = document.getElementById('autoAnalyzeCheckbox')
    if (autoAnalyzeCheckbox) {
      autoAnalyzeCheckbox.checked = this.settings.autoAnalyze || false
    }

    const showPreviewCheckbox = document.getElementById('showPreviewCheckbox')
    if (showPreviewCheckbox) {
      showPreviewCheckbox.checked = this.settings.showPreview !== false
    }
  }

  updateProfileList () {
    const profileList = document.getElementById('profileList')
    if (!profileList) return

    profileList.innerHTML = ''

    if (this.profiles.length === 0) {
      profileList.innerHTML = `
        <div class="empty-state">
          <p>No profiles found. Create your first profile to get started.</p>
        </div>
      `
      return
    }

    this.profiles.forEach(profile => {
      const profileItem = document.createElement('div')
      profileItem.className = `profile-item ${this.selectedProfile === profile.id ? 'selected' : ''}`
      profileItem.innerHTML = `
        <div class="profile-info">
          <div class="profile-name">${profile.name}</div>
          <div class="profile-details">${profile.info.email || 'No email'}</div>
        </div>
      `

      profileItem.addEventListener('click', () => {
        this.selectProfile(profile.id)
      })

      profileList.appendChild(profileItem)
    })
  }

  selectProfile (profileId) {
    this.selectedProfile = profileId
    this.updateProfileList()
    this.updateUI()
  }

  updateUI () {
    const autofillBtn = document.getElementById('autofillBtn')
    if (autofillBtn) {
      autofillBtn.disabled = !this.selectedProfile || !this.settings.apiKey
    }

    this.updateStatusIndicator()
  }

  updateStatusIndicator () {
    const statusIndicator = document.getElementById('statusIndicator')
    const statusText = statusIndicator.querySelector('.status-text')
    const statusDot = statusIndicator.querySelector('.status-dot')

    if (!this.settings.apiKey) {
      statusText.textContent = 'API key required'
      statusDot.style.background = 'var(--warning-color)'
    } else if (!this.selectedProfile) {
      statusText.textContent = 'Select profile'
      statusDot.style.background = 'var(--warning-color)'
    } else {
      statusText.textContent = 'Ready'
      statusDot.style.background = 'var(--success-color)'
    }
  }

  async saveSettings () {
    try {
      const apiKey = document.getElementById('apiKeyInput').value.trim()
      const autoAnalyze = document.getElementById('autoAnalyzeCheckbox').checked
      const showPreview = document.getElementById('showPreviewCheckbox').checked

      const newSettings = {
        ...this.settings,
        apiKey,
        autoAnalyze,
        showPreview
      }

      const response = await chrome.runtime.sendMessage({
        action: 'saveSettings',
        settings: newSettings
      })

      if (response.success) {
        this.settings = newSettings
        this.updateUI()
        this.showNotification('Settings saved successfully', 'success')
      } else {
        throw new Error('Failed to save settings')
      }
    } catch (error) {
      console.error('Failed to save settings:', error)
      this.showNotification('Failed to save settings', 'error')
    }
  }

  async saveProfile () {
    try {
      const profileData = {
        name: document.getElementById('profileNameInput').value.trim(),
        info: {
          firstName: document.getElementById('firstNameInput').value.trim(),
          lastName: document.getElementById('lastNameInput').value.trim(),
          email: document.getElementById('emailInput').value.trim(),
          phone: document.getElementById('phoneInput').value.trim(),
          addressLine1: document.getElementById('addressLine1Input').value.trim(),
          addressLine2: document.getElementById('addressLine2Input').value.trim(),
          city: document.getElementById('cityInput').value.trim(),
          state: document.getElementById('stateInput').value.trim(),
          postalCode: document.getElementById('postalCodeInput').value.trim(),
          country: document.getElementById('countryInput').value.trim()
        }
      }

      if (!profileData.name) {
        this.showNotification('Profile name is required', 'error')
        return
      }

      const { ProfileService } = await import('../shared/services/profile-service.js')
      const profileId = await ProfileService.createProfile(profileData)

      await this.loadProfiles()
      this.selectProfile(profileId)
      this.showView('profileView')
      this.showNotification('Profile created successfully', 'success')
    } catch (error) {
      console.error('Failed to save profile:', error)
      this.showNotification('Failed to create profile', 'error')
    }
  }

  async analyzePage () {
    try {
      this.showView('statusView')
      this.updateStatusMessage('Analyzing page structure...')

      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true })

      if (!tab) {
        throw new Error('No active tab found')
      }

      const response = await chrome.tabs.sendMessage(tab.id, {
        action: 'analyzePage'
      })

      if (response && response.success) {
        this.showNotification(`Found ${response.fieldCount} fields in ${response.formCount} forms`, 'success')
        this.showView('profileView')
      } else {
        throw new Error(response?.error || 'Analysis failed')
      }
    } catch (error) {
      console.error('Page analysis failed:', error)
      this.showNotification(error.message, 'error')
      this.showView('profileView')
    }
  }

  async performAutofill () {
    if (!this.selectedProfile) {
      this.showNotification('Please select a profile first', 'error')
      return
    }

    if (!this.settings.apiKey) {
      this.showNotification('Please configure your API key in settings', 'error')
      this.showView('settingsView')
      return
    }

    try {
      console.log('Starting autofill process from sidebar...')
      this.showView('statusView')
      this.updateStatusMessage('Preparing autofill...')

      // Get the selected profile
      const { ProfileService } = await import('../shared/services/profile-service.js')
      const profile = await ProfileService.getProfile(this.selectedProfile)

      // Get current tab
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true })

      if (!tab) {
        throw new Error('No active tab found')
      }

      // Check if tab URL is valid for content script injection
      if (tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://') ||
          tab.url.startsWith('moz-extension://') || tab.url.startsWith('about:')) {
        throw new Error('Cannot autofill on system pages. Please navigate to a regular webpage with forms.')
      }

      this.updateStatusMessage('Performing autofill...')

      // Send message to content script to perform autofill
      const response = await chrome.tabs.sendMessage(tab.id, {
        action: 'performAutofill',
        profileInfo: profile.info,
        settings: this.settings
      })

      if (response && response.success) {
        this.showAutofillResults(response.results)
      } else {
        const errorMessage = response?.error || 'Autofill failed - no response from content script'
        throw new Error(errorMessage)
      }
    } catch (error) {
      console.error('Autofill failed:', error)
      this.showNotification(error.message, 'error')
      this.showView('profileView')
    }
  }

  showAutofillResults (results) {
    // Implementation for showing results
    this.showNotification('Autofill completed successfully', 'success')
    this.showView('profileView')
  }

  updateStatusMessage (message) {
    const statusMessage = document.getElementById('statusMessage')
    if (statusMessage) {
      statusMessage.textContent = message
    }
  }

  showNotification (message, type = 'info') {
    // Simple notification implementation
    console.log(`${type.toUpperCase()}: ${message}`)

    // You could implement a toast notification system here
    // For now, we'll use the status indicator
    const statusText = document.querySelector('.status-text')
    if (statusText) {
      const originalText = statusText.textContent
      statusText.textContent = message

      setTimeout(() => {
        statusText.textContent = originalText
      }, 3000)
    }
  }
}

// Initialize sidebar when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  const sidebarController = new SidebarController()
  window.sidebarController = sidebarController // For debugging
})
