// src/background/background.js
// Background service worker for Smart Autofill Assistant
// Handles extension lifecycle, permissions, and cross-tab communication

import { StorageUtils } from '../shared/utils/storage-utils.js'

class BackgroundService {
  constructor () {
    this.init()
  }

  init () {
    this.setupEventListeners()
    this.setupContextMenus()
    this.setupSidePanel()
  }

  setupEventListeners () {
    // Extension installation/update
    chrome.runtime.onInstalled.addListener((details) => {
      this.handleInstallation(details)
    })

    // Message handling
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse)
      return true // Keep message channel open for async responses
    })

    // Tab updates
    chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
      this.handleTabUpdate(tabId, changeInfo, tab)
    })

    // Action button click
    chrome.action.onClicked.addListener((tab) => {
      this.handleActionClick(tab)
    })
  }

  async handleInstallation (details) {
    console.log('Extension installed/updated:', details)

    if (details.reason === 'install') {
      // First installation
      await this.initializeDefaultSettings()
      await this.createWelcomeProfile()
    } else if (details.reason === 'update') {
      // Extension update
      await this.migrateSettings(details.previousVersion)
    }
  }

  async initializeDefaultSettings () {
    try {
      const defaultSettings = {
        apiKey: '',
        modelConfig: {
          temperature: 0.1,
          maxOutputTokens: 1000,
          thinkingBudget: 0
        },
        uiConfig: {
          autoAnalyze: true,
          showConfirmation: true,
          sidebarPosition: 'right'
        },
        privacyConfig: {
          storeLocally: true,
          encryptData: false,
          clearOnExit: false
        }
      }

      await StorageUtils.set('autofill_settings', defaultSettings)
      console.log('Default settings initialized')
    } catch (error) {
      console.error('Failed to initialize default settings:', error)
    }
  }

  async createWelcomeProfile () {
    try {
      const welcomeProfile = {
        id: `welcome_profile_${Date.now()}`,
        name: 'Welcome Profile',
        info: 'This is a sample profile to help you get started. Edit this profile or create a new one with your personal information. You can include your name, email, phone number, address, and any other details you commonly fill in forms.',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      await StorageUtils.set('autofill_profiles', [welcomeProfile])
      console.log('Welcome profile created')
    } catch (error) {
      console.error('Failed to create welcome profile:', error)
    }
  }

  async migrateSettings (previousVersion) {
    try {
      console.log(`Migrating from version ${previousVersion}`)

      // Add migration logic here for future versions
      const settings = await StorageUtils.get('autofill_settings', {})

      // Ensure all required settings exist
      if (!settings.privacyConfig) {
        settings.privacyConfig = {
          storeLocally: true,
          encryptData: false,
          clearOnExit: false
        }
      }

      await StorageUtils.set('autofill_settings', settings)
      console.log('Settings migration completed')
    } catch (error) {
      console.error('Failed to migrate settings:', error)
    }
  }

  async handleMessage (message, sender, sendResponse) {
    try {
      switch (message.action) {
        case 'getSettings': {
          const settings = await StorageUtils.get('autofill_settings', {})
          sendResponse({ success: true, data: settings })
          break
        }

        case 'saveSettings': {
          await StorageUtils.set('autofill_settings', message.settings)
          sendResponse({ success: true })
          break
        }

        case 'analyzeTab': {
          const result = await this.analyzeCurrentTab(sender.tab.id)
          sendResponse(result)
          break
        }

        case 'performAutofill': {
          const autofillResult = await this.performAutofillOnTab(
            sender.tab.id,
            message.profileInfo,
            message.settings
          )
          sendResponse(autofillResult)
          break
        }

        case 'checkPermissions': {
          const hasPermissions = await this.checkRequiredPermissions()
          sendResponse({ success: true, hasPermissions })
          break
        }

        default:
          sendResponse({ success: false, error: 'Unknown action' })
      }
    } catch (error) {
      console.error('Error handling message:', error)
      sendResponse({ success: false, error: error.message })
    }
  }

  async handleTabUpdate (tabId, changeInfo, tab) {
    // Only process when page is completely loaded
    if (changeInfo.status !== 'complete') return

    try {
      const settings = await StorageUtils.get('autofill_settings', {})

      // Auto-analyze if enabled
      if (settings.uiConfig?.autoAnalyze) {
        await this.injectContentScript(tabId)
      }
    } catch (error) {
      console.error('Error handling tab update:', error)
    }
  }

  async handleActionClick (tab) {
    try {
      // Ensure content script is injected
      await this.injectContentScript(tab.id)

      // Open sidebar panel
      await chrome.sidePanel.open({ tabId: tab.id })
      console.log('Sidebar opened for tab:', tab.id)
    } catch (error) {
      console.error('Error handling action click:', error)
    }
  }

  async injectContentScript (tabId) {
    try {
      console.log('Checking if content script is already injected in tab:', tabId)

      // Check if content script is already injected
      const response = await chrome.tabs.sendMessage(tabId, { action: 'ping' })
      if (response?.success) {
        console.log('Content script already injected in tab:', tabId)
        return true // Already injected
      }
    } catch (error) {
      console.log('Content script not injected, proceeding with injection...')
      // Content script not injected, proceed with injection
    }

    try {
      // Get tab info to check if injection is possible
      const tab = await chrome.tabs.get(tabId)
      console.log('Tab info:', { url: tab.url, status: tab.status })

      // Check if URL is injectable
      if (tab.url.startsWith('chrome://') || tab.url.startsWith('chrome-extension://') ||
          tab.url.startsWith('moz-extension://') || tab.url.startsWith('about:')) {
        console.error('Cannot inject content script into system page:', tab.url)
        throw new Error('Cannot inject content script into system pages')
      }

      // Wait for tab to be ready
      if (tab.status !== 'complete') {
        console.log('Waiting for tab to complete loading...')
        await new Promise((resolve) => {
          const listener = (updatedTabId, changeInfo) => {
            if (updatedTabId === tabId && changeInfo.status === 'complete') {
              chrome.tabs.onUpdated.removeListener(listener)
              resolve()
            }
          }
          chrome.tabs.onUpdated.addListener(listener)
        })
      }

      console.log('Injecting content script into tab:', tabId)
      await chrome.scripting.executeScript({
        target: { tabId },
        files: ['src/content/content-main.js']
      })

      // Verify injection was successful
      await new Promise(resolve => setTimeout(resolve, 100)) // Small delay
      const verifyResponse = await chrome.tabs.sendMessage(tabId, { action: 'ping' })
      if (verifyResponse?.success) {
        console.log('Content script successfully injected and verified in tab:', tabId)
        return true
      } else {
        throw new Error('Content script injection verification failed')
      }
    } catch (error) {
      console.error('Failed to inject content script:', error)
      throw error
    }
  }

  async analyzeCurrentTab (tabId) {
    try {
      await this.injectContentScript(tabId)

      const response = await chrome.tabs.sendMessage(tabId, {
        action: 'analyzePage'
      })

      return response
    } catch (error) {
      console.error('Failed to analyze tab:', error)
      return { success: false, error: error.message }
    }
  }

  async performAutofillOnTab (tabId, profileInfo, settings) {
    try {
      await this.injectContentScript(tabId)

      const response = await chrome.tabs.sendMessage(tabId, {
        action: 'performAutofill',
        profileInfo,
        settings
      })

      return response
    } catch (error) {
      console.error('Failed to perform autofill:', error)
      return { success: false, error: error.message }
    }
  }

  async checkRequiredPermissions () {
    try {
      const permissions = await chrome.permissions.getAll()
      const requiredPermissions = ['storage', 'activeTab', 'scripting']

      return requiredPermissions.every(permission =>
        permissions.permissions.includes(permission)
      )
    } catch (error) {
      console.error('Failed to check permissions:', error)
      return false
    }
  }

  setupSidePanel () {
    try {
      // Enable side panel for all tabs
      chrome.sidePanel.setPanelBehavior({ openPanelOnActionClick: true })
      console.log('Side panel configured')
    } catch (error) {
      console.error('Failed to setup side panel:', error)
    }
  }

  setupContextMenus () {
    try {
      chrome.contextMenus.create({
        id: 'autofill-form',
        title: 'Autofill with Smart Assistant',
        contexts: ['editable'],
        documentUrlPatterns: ['http://*/*', 'https://*/*']
      })

      chrome.contextMenus.onClicked.addListener((info, tab) => {
        if (info.menuItemId === 'autofill-form') {
          this.handleContextMenuClick(tab)
        }
      })
    } catch (error) {
      console.error('Failed to setup context menus:', error)
    }
  }

  async handleContextMenuClick (tab) {
    try {
      // Open popup or trigger autofill
      await this.injectContentScript(tab.id)

      // Could trigger a specific action here
      console.log('Context menu clicked for tab:', tab.id)
    } catch (error) {
      console.error('Error handling context menu click:', error)
    }
  }
}

// Initialize background service
const backgroundService = new BackgroundService()
console.log('Background service initialized:', backgroundService)
