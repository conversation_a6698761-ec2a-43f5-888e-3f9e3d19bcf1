// src/content/content-main.js
// Main content script for Smart Autofill Assistant
// Handles page analysis, form detection, and field injection

import { AutofillService } from '../shared/services/autofill-service.js'
import { DOMUtils } from '../shared/utils/dom-utils.js'
import { FormModel } from '../shared/models/form-model.js'

class ContentScript {
  constructor () {
    this.autofillService = null
    this.currentFormModel = null
    this.isAnalyzing = false

    this.init()
  }

  init () {
    this.setupMessageListener()
    this.observePageChanges()
    console.log('Smart Autofill content script loaded')
  }

  setupMessageListener () {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse)
      return true // Keep message channel open for async responses
    })
  }

  async handleMessage (message, sender, sendResponse) {
    try {
      console.log('Content script received message:', message.action)
      switch (message.action) {
        case 'ping':
          console.log('Content script ping received')
          sendResponse({ success: true, status: 'ready' })
          break

        case 'analyzePage': {
          const analysisResult = await this.analyzePage()
          sendResponse(analysisResult)
          break
        }

        case 'performAutofill': {
          const autofillResult = await this.performAutofill(
            message.profileInfo,
            message.settings
          )
          sendResponse(autofillResult)
          break
        }

        case 'previewAutofill': {
          const previewResult = await this.previewAutofill(
            message.profileInfo,
            message.settings
          )
          sendResponse(previewResult)
          break
        }

        case 'highlightFields':
          this.highlightDetectedFields()
          sendResponse({ success: true })
          break

        case 'clearHighlights':
          this.clearFieldHighlights()
          sendResponse({ success: true })
          break

        default:
          sendResponse({ success: false, error: 'Unknown action' })
      }
    } catch (error) {
      console.error('Content script error:', error)
      sendResponse({ success: false, error: error.message })
    }
  }

  async analyzePage () {
    if (this.isAnalyzing) {
      console.log('Analysis already in progress, skipping...')
      return { success: false, error: 'Analysis already in progress' }
    }

    this.isAnalyzing = true
    console.log('Starting page analysis...')

    try {
      // Wait for page to be fully loaded
      if (document.readyState !== 'complete') {
        console.log('Waiting for page to load completely...')
        await new Promise(resolve => {
          if (document.readyState === 'complete') {
            resolve()
          } else {
            window.addEventListener('load', resolve, { once: true })
          }
        })
      }

      // Find forms on the page
      console.log('Searching for forms on the page...')
      const forms = DOMUtils.findForms()
      console.log(`Found ${forms.length} forms on the page`)

      if (forms.length === 0) {
        console.log('No forms found, checking for common form patterns...')

        // Check for common form patterns that might not be in <form> tags
        const inputFields = document.querySelectorAll('input[type="text"], input[type="email"], input[type="tel"], input[type="password"], textarea')
        if (inputFields.length > 0) {
          console.log(`Found ${inputFields.length} input fields outside of form tags`)
          return {
            success: false,
            error: `Found ${inputFields.length} input fields but no proper form structure. The page may use custom form handling.`,
            formCount: 0,
            fieldCount: inputFields.length
          }
        }

        return {
          success: false,
          error: 'No forms or input fields found on this page. Make sure you are on a page with a form.',
          formCount: 0,
          fieldCount: 0
        }
      }

      // Analyze the most relevant form
      console.log('Selecting best form for analysis...')
      const targetForm = this.selectBestForm(forms)
      console.log('Selected form:', targetForm)

      console.log('Analyzing form structure...')
      const formModel = await this.analyzeForm(targetForm)
      console.log('Form analysis completed:', formModel)

      this.currentFormModel = formModel

      const result = {
        success: true,
        formModel: formModel.toJSON(),
        formCount: forms.length,
        fieldCount: formModel.fields.length
      }

      console.log('Page analysis successful:', result)
      return result
    } catch (error) {
      console.error('Page analysis failed with error:', error)
      console.error('Error stack:', error.stack)

      let errorMessage = 'Failed to analyze page structure'
      if (error.message.includes('DOMUtils')) {
        errorMessage = 'Failed to analyze page DOM. The page structure may be incompatible.'
      } else if (error.message.includes('FormModel')) {
        errorMessage = 'Failed to create form model. The form structure may be invalid.'
      }

      return { success: false, error: errorMessage }
    } finally {
      this.isAnalyzing = false
      console.log('Page analysis completed')
    }
  }

  selectBestForm (forms) {
    // Prioritize forms with more visible fields
    return forms.reduce((best, current) => {
      const bestScore = this.scoreForm(best)
      const currentScore = this.scoreForm(current)
      return currentScore > bestScore ? current : best
    })
  }

  scoreForm (form) {
    const fields = DOMUtils.findFormFields(form)
    let score = fields.length

    // Bonus for forms with common field types
    const commonTypes = ['email', 'tel', 'text', 'textarea']
    fields.forEach(field => {
      if (commonTypes.includes(field.type)) {
        score += 2
      }
      if (field.required) {
        score += 1
      }
    })

    // Penalty for hidden forms
    const rect = form.getBoundingClientRect()
    if (rect.width === 0 || rect.height === 0) {
      score -= 10
    }

    return score
  }

  async analyzeForm (formElement) {
    const formModel = new FormModel({
      formSelector: DOMUtils.getFieldSelector(formElement)
    })

    const fields = DOMUtils.findFormFields(formElement)

    for (const fieldElement of fields) {
      const fieldModel = {
        selector: DOMUtils.getFieldSelector(fieldElement),
        type: DOMUtils.getFieldType(fieldElement),
        label: DOMUtils.getFieldLabel(fieldElement),
        placeholder: fieldElement.placeholder || '',
        name: fieldElement.name || '',
        id: fieldElement.id || '',
        required: fieldElement.required || false,
        element: fieldElement
      }

      formModel.addField(fieldModel)
    }

    return formModel
  }

  async performAutofill (profileInfo, settings) {
    try {
      console.log('Starting autofill process...', { profileInfo, settings: { ...settings, apiKey: settings.apiKey ? '[REDACTED]' : 'MISSING' } })

      // Validate inputs
      if (!profileInfo) {
        console.error('Profile info is missing')
        return { success: false, error: 'Profile information is required' }
      }

      if (!settings) {
        console.error('Settings are missing')
        return { success: false, error: 'Settings are required' }
      }

      // Initialize autofill service with API key
      if (!settings.apiKey) {
        console.error('API key is missing from settings')
        return { success: false, error: 'API key not configured. Please set your Gemini API key in settings.' }
      }

      console.log('Initializing autofill service...')
      this.autofillService = new AutofillService(settings.apiKey)

      // Analyze page if not already done
      if (!this.currentFormModel) {
        console.log('Analyzing page for forms...')
        const analysisResult = await this.analyzePage()
        if (!analysisResult.success) {
          console.error('Page analysis failed:', analysisResult.error)
          return analysisResult
        }
        console.log('Page analysis completed successfully')
      }

      console.log('Performing autofill with form model:', this.currentFormModel)

      // Perform autofill
      const result = await this.autofillService.fillForm(
        this.currentFormModel,
        profileInfo
      )

      console.log('Autofill completed:', result)

      // Add visual feedback
      this.showAutofillFeedback(result)

      return {
        success: true,
        results: result,
        fieldsProcessed: this.currentFormModel.fields.length
      }
    } catch (error) {
      console.error('Autofill failed with error:', error)
      console.error('Error stack:', error.stack)

      // Provide more specific error messages
      let errorMessage = 'Failed to perform autofill'
      if (error.message.includes('API')) {
        errorMessage = 'API connection failed. Please check your internet connection and API key.'
      } else if (error.message.includes('form')) {
        errorMessage = 'No suitable forms found on this page.'
      } else if (error.message.includes('inject')) {
        errorMessage = 'Failed to fill form fields. The page may have security restrictions.'
      }

      return { success: false, error: errorMessage }
    }
  }

  async previewAutofill (profileInfo, settings) {
    try {
      if (!settings.apiKey) {
        return { success: false, error: 'API key not configured' }
      }

      this.autofillService = new AutofillService(settings.apiKey)

      // Analyze page if not already done
      if (!this.currentFormModel) {
        const analysisResult = await this.analyzePage()
        if (!analysisResult.success) {
          return analysisResult
        }
      }

      // Get preview without actually filling
      const preview = await this.autofillService.previewAutofill(
        this.currentFormModel,
        profileInfo
      )

      return preview
    } catch (error) {
      console.error('Autofill preview failed:', error)
      return { success: false, error: error.message }
    }
  }

  showAutofillFeedback (results) {
    // Highlight filled fields
    results.filled.forEach(item => {
      const field = this.currentFormModel.getFieldBySelector(item.selector)
      if (field && field.element) {
        DOMUtils.highlightField(field.element, 3000)
      }
    })

    // Show notification
    this.showNotification(
      `Autofill complete: ${results.filled.length} fields filled`,
      'success'
    )
  }

  highlightDetectedFields () {
    if (!this.currentFormModel) return

    this.currentFormModel.fields.forEach(field => {
      if (field.element) {
        field.element.style.outline = '2px solid #667eea'
        field.element.style.outlineOffset = '2px'
      }
    })

    // Clear highlights after 5 seconds
    setTimeout(() => {
      this.clearFieldHighlights()
    }, 5000)
  }

  clearFieldHighlights () {
    if (!this.currentFormModel) return

    this.currentFormModel.fields.forEach(field => {
      if (field.element) {
        field.element.style.outline = ''
        field.element.style.outlineOffset = ''
      }
    })
  }

  showNotification (message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div')
    notification.className = `autofill-notification autofill-notification-${type}`
    notification.textContent = message

    // Style the notification
    Object.assign(notification.style, {
      position: 'fixed',
      top: '20px',
      right: '20px',
      background: type === 'success' ? '#38a169' : '#3182ce',
      color: 'white',
      padding: '12px 20px',
      borderRadius: '8px',
      boxShadow: '0 8px 25px rgba(0, 0, 0, 0.15)',
      zIndex: '10000',
      fontSize: '14px',
      fontFamily: 'system-ui, sans-serif',
      maxWidth: '300px',
      animation: 'slideInRight 0.3s ease-out'
    })

    // Add animation styles
    const style = document.createElement('style')
    style.textContent = `
      @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
      }
      @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
      }
    `
    document.head.appendChild(style)

    // Add to page
    document.body.appendChild(notification)

    // Remove after 3 seconds
    setTimeout(() => {
      notification.style.animation = 'slideOutRight 0.3s ease-in'
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification)
        }
      }, 300)
    }, 3000)
  }

  observePageChanges () {
    // Watch for dynamic form changes
    const observer = new MutationObserver((mutations) => {
      let shouldReanalyze = false

      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          // Check if forms were added or removed
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              if (node.tagName === 'FORM' || node.querySelector('form')) {
                shouldReanalyze = true
              }
            }
          })
        }
      })

      if (shouldReanalyze) {
        // Clear current form model to trigger re-analysis
        this.currentFormModel = null
        console.log('Page structure changed, form analysis reset')
      }
    })

    observer.observe(document.body, {
      childList: true,
      subtree: true
    })
  }
}

// Initialize content script when DOM is ready
let contentScript
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    contentScript = new ContentScript()
  })
} else {
  contentScript = new ContentScript()
}

// Export for debugging
window.contentScript = contentScript
