// src/content/dom-analyzer.js
// Advanced DOM analysis for form structure understanding
// Provides detailed form field detection and semantic analysis

import { SEMANTIC_LABELS } from '../shared/constants/field-types.js'
import { DOMUtils } from '../shared/utils/dom-utils.js'

export class DOMAnalyzer {
  constructor () {
    this.fieldPatterns = this.initializeFieldPatterns()
  }

  initializeFieldPatterns () {
    return {
      [SEMANTIC_LABELS.EMAIL]: [
        /email/i,
        /e-mail/i,
        /mail/i,
        /@/
      ],
      [SEMANTIC_LABELS.FIRST_NAME]: [
        /first.*name/i,
        /given.*name/i,
        /fname/i,
        /firstname/i
      ],
      [SEMANTIC_LABELS.LAST_NAME]: [
        /last.*name/i,
        /family.*name/i,
        /surname/i,
        /lname/i,
        /lastname/i
      ],
      [SEMANTIC_LABELS.FULL_NAME]: [
        /^name$/i,
        /full.*name/i,
        /complete.*name/i,
        /your.*name/i
      ],
      [SEMANTIC_LABELS.PHONE]: [
        /phone/i,
        /mobile/i,
        /cell/i,
        /tel/i,
        /number/i
      ],
      [SEMANTIC_LABELS.ADDRESS_LINE_1]: [
        /address/i,
        /street/i,
        /addr/i,
        /line.*1/i
      ],
      [SEMANTIC_LABELS.ADDRESS_LINE_2]: [
        /address.*2/i,
        /line.*2/i,
        /apt/i,
        /apartment/i,
        /suite/i,
        /unit/i
      ],
      [SEMANTIC_LABELS.CITY]: [
        /city/i,
        /town/i,
        /locality/i
      ],
      [SEMANTIC_LABELS.STATE]: [
        /state/i,
        /province/i,
        /region/i
      ],
      [SEMANTIC_LABELS.POSTAL_CODE]: [
        /zip/i,
        /postal/i,
        /postcode/i,
        /zipcode/i
      ],
      [SEMANTIC_LABELS.COUNTRY]: [
        /country/i,
        /nation/i
      ],
      [SEMANTIC_LABELS.COMPANY]: [
        /company/i,
        /organization/i,
        /employer/i,
        /business/i
      ],
      [SEMANTIC_LABELS.JOB_TITLE]: [
        /title/i,
        /position/i,
        /job/i,
        /role/i
      ],
      [SEMANTIC_LABELS.WEBSITE]: [
        /website/i,
        /url/i,
        /site/i,
        /homepage/i
      ],
      [SEMANTIC_LABELS.USERNAME]: [
        /username/i,
        /user.*name/i,
        /login/i,
        /account/i
      ],
      [SEMANTIC_LABELS.PASSWORD]: [
        /password/i,
        /pass/i,
        /pwd/i
      ],
      [SEMANTIC_LABELS.DATE_OF_BIRTH]: [
        /birth/i,
        /dob/i,
        /birthday/i,
        /born/i
      ]
    }
  }

  analyzeFormStructure (formElement) {
    const analysis = {
      formElement,
      fields: [],
      formType: this.detectFormType(formElement),
      complexity: 'simple',
      hasRequiredFields: false,
      hasValidation: false
    }

    const fields = DOMUtils.findFormFields(formElement)

    for (const fieldElement of fields) {
      const fieldAnalysis = this.analyzeField(fieldElement)
      analysis.fields.push(fieldAnalysis)

      if (fieldAnalysis.required) {
        analysis.hasRequiredFields = true
      }

      if (fieldAnalysis.hasValidation) {
        analysis.hasValidation = true
      }
    }

    // Determine complexity
    analysis.complexity = this.determineComplexity(analysis)

    return analysis
  }

  analyzeField (fieldElement) {
    const analysis = {
      element: fieldElement,
      selector: DOMUtils.getFieldSelector(fieldElement),
      type: DOMUtils.getFieldType(fieldElement),
      label: this.extractFieldLabel(fieldElement),
      placeholder: fieldElement.placeholder || '',
      name: fieldElement.name || '',
      id: fieldElement.id || '',
      required: fieldElement.required || false,
      semanticLabel: null,
      confidence: 0,
      context: this.getFieldContext(fieldElement),
      hasValidation: this.hasValidationAttributes(fieldElement),
      autocomplete: fieldElement.autocomplete || ''
    }

    // Determine semantic label
    const semanticResult = this.determineSemanticLabel(analysis)
    analysis.semanticLabel = semanticResult.label
    analysis.confidence = semanticResult.confidence

    return analysis
  }

  extractFieldLabel (fieldElement) {
    // Try multiple methods to find the label
    const labelMethods = [
      () => this.getLabelByFor(fieldElement),
      () => this.getLabelByParent(fieldElement),
      () => this.getLabelByPrevious(fieldElement),
      () => this.getLabelByPlaceholder(fieldElement),
      () => this.getLabelByAriaLabel(fieldElement),
      () => this.getLabelByTitle(fieldElement)
    ]

    for (const method of labelMethods) {
      const label = method()
      if (label && label.trim()) {
        return label.trim()
      }
    }

    return ''
  }

  getLabelByFor (fieldElement) {
    if (!fieldElement.id) return null

    const label = document.querySelector(`label[for="${fieldElement.id}"]`)
    return label ? label.textContent : null
  }

  getLabelByParent (fieldElement) {
    const parentLabel = fieldElement.closest('label')
    if (parentLabel) {
      // Get text content excluding the input itself
      const clone = parentLabel.cloneNode(true)
      const inputs = clone.querySelectorAll('input, textarea, select')
      inputs.forEach(input => input.remove())
      return clone.textContent
    }
    return null
  }

  getLabelByPrevious (fieldElement) {
    let sibling = fieldElement.previousElementSibling

    while (sibling) {
      if (sibling.tagName === 'LABEL') {
        return sibling.textContent
      }

      // Check for text content in other elements
      if (sibling.textContent && sibling.textContent.trim()) {
        const text = sibling.textContent.trim()
        if (text.length < 100) { // Reasonable label length
          return text
        }
      }

      sibling = sibling.previousElementSibling
    }

    return null
  }

  getLabelByPlaceholder (fieldElement) {
    return fieldElement.placeholder
  }

  getLabelByAriaLabel (fieldElement) {
    return fieldElement.getAttribute('aria-label')
  }

  getLabelByTitle (fieldElement) {
    return fieldElement.title
  }

  getFieldContext (fieldElement) {
    const context = {
      parentForm: fieldElement.closest('form'),
      fieldset: fieldElement.closest('fieldset'),
      container: fieldElement.closest('div, section, article'),
      nearbyText: this.getNearbyText(fieldElement)
    }

    return context
  }

  getNearbyText (fieldElement) {
    const container = fieldElement.closest('div, section, article, form')
    if (!container) return ''

    const walker = document.createTreeWalker(
      container,
      NodeFilter.SHOW_TEXT,
      {
        acceptNode: (node) => {
          const parent = node.parentElement
          if (!parent) return NodeFilter.FILTER_REJECT

          // Skip script and style elements
          if (['SCRIPT', 'STYLE'].includes(parent.tagName)) {
            return NodeFilter.FILTER_REJECT
          }

          // Skip if text is too far from the field
          const rect1 = fieldElement.getBoundingClientRect()
          const rect2 = parent.getBoundingClientRect()
          const distance = Math.abs(rect1.top - rect2.top) + Math.abs(rect1.left - rect2.left)

          if (distance > 200) return NodeFilter.FILTER_REJECT

          return NodeFilter.FILTER_ACCEPT
        }
      }
    )

    const textNodes = []
    let node
    while ((node = walker.nextNode())) {
      const text = node.textContent.trim()
      if (text && text.length > 2) {
        textNodes.push(text)
      }
    }

    return textNodes.join(' ').substring(0, 500)
  }

  determineSemanticLabel (fieldAnalysis) {
    const searchText = [
      fieldAnalysis.label,
      fieldAnalysis.placeholder,
      fieldAnalysis.name,
      fieldAnalysis.id,
      fieldAnalysis.autocomplete,
      fieldAnalysis.context.nearbyText
    ].join(' ').toLowerCase()

    let bestMatch = { label: null, confidence: 0 }

    for (const [semanticLabel, patterns] of Object.entries(this.fieldPatterns)) {
      let confidence = 0

      for (const pattern of patterns) {
        if (pattern.test(searchText)) {
          confidence += 0.3
        }
      }

      // Boost confidence for exact matches in important attributes
      if (fieldAnalysis.type === 'email' && semanticLabel === SEMANTIC_LABELS.EMAIL) {
        confidence += 0.5
      }

      if (fieldAnalysis.type === 'tel' && semanticLabel === SEMANTIC_LABELS.PHONE) {
        confidence += 0.5
      }

      if (fieldAnalysis.type === 'password' && semanticLabel === SEMANTIC_LABELS.PASSWORD) {
        confidence += 0.5
      }

      // Autocomplete attribute matching
      if (fieldAnalysis.autocomplete) {
        const autocompleteMap = {
          email: SEMANTIC_LABELS.EMAIL,
          'given-name': SEMANTIC_LABELS.FIRST_NAME,
          'family-name': SEMANTIC_LABELS.LAST_NAME,
          name: SEMANTIC_LABELS.FULL_NAME,
          tel: SEMANTIC_LABELS.PHONE,
          'street-address': SEMANTIC_LABELS.ADDRESS_LINE_1,
          'address-line2': SEMANTIC_LABELS.ADDRESS_LINE_2,
          locality: SEMANTIC_LABELS.CITY,
          region: SEMANTIC_LABELS.STATE,
          'postal-code': SEMANTIC_LABELS.POSTAL_CODE,
          country: SEMANTIC_LABELS.COUNTRY,
          organization: SEMANTIC_LABELS.COMPANY,
          'organization-title': SEMANTIC_LABELS.JOB_TITLE,
          url: SEMANTIC_LABELS.WEBSITE,
          username: SEMANTIC_LABELS.USERNAME,
          'current-password': SEMANTIC_LABELS.PASSWORD,
          'new-password': SEMANTIC_LABELS.PASSWORD,
          bday: SEMANTIC_LABELS.DATE_OF_BIRTH
        }

        if (autocompleteMap[fieldAnalysis.autocomplete] === semanticLabel) {
          confidence += 0.7
        }
      }

      if (confidence > bestMatch.confidence) {
        bestMatch = { label: semanticLabel, confidence }
      }
    }

    return bestMatch
  }

  detectFormType (formElement) {
    const formText = formElement.textContent.toLowerCase()
    const formAction = (formElement.action || '').toLowerCase()

    if (formText.includes('login') || formText.includes('sign in') || formAction.includes('login')) {
      return 'login'
    }

    if (formText.includes('register') || formText.includes('sign up') || formAction.includes('register')) {
      return 'registration'
    }

    if (formText.includes('contact') || formAction.includes('contact')) {
      return 'contact'
    }

    if (formText.includes('checkout') || formText.includes('payment') || formAction.includes('checkout')) {
      return 'checkout'
    }

    if (formText.includes('profile') || formText.includes('account') || formAction.includes('profile')) {
      return 'profile'
    }

    return 'general'
  }

  determineComplexity (analysis) {
    const fieldCount = analysis.fields.length
    const uniqueSemanticLabels = new Set(
      analysis.fields
        .filter(f => f.semanticLabel)
        .map(f => f.semanticLabel)
    ).size

    if (fieldCount <= 3) return 'simple'
    if (fieldCount <= 8 && uniqueSemanticLabels <= 5) return 'medium'
    return 'complex'
  }

  hasValidationAttributes (fieldElement) {
    const validationAttributes = [
      'required',
      'pattern',
      'min',
      'max',
      'minlength',
      'maxlength',
      'step'
    ]

    return validationAttributes.some(attr => fieldElement.hasAttribute(attr))
  }

  generateFieldSummary (analysis) {
    return {
      totalFields: analysis.fields.length,
      requiredFields: analysis.fields.filter(f => f.required).length,
      identifiedFields: analysis.fields.filter(f => f.confidence > 0.5).length,
      formType: analysis.formType,
      complexity: analysis.complexity,
      hasValidation: analysis.hasValidation
    }
  }
}
