// src/shared/utils/validation-utils.js
// Data validation utility functions
// Provides validation methods for profiles, settings, and form data

export class ValidationUtils {
  static isValidEmail (email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  static isValidPhone (phone) {
    const phoneRegex = /^[+]?[1-9][\d]{0,15}$/
    return phoneRegex.test(phone.replace(/[\s\-()]/g, ''))
  }

  static isValidUrl (url) {
    try {
      const urlObj = new URL(url)
      return !!urlObj
    } catch {
      return false
    }
  }

  static isValidDate (date) {
    const dateObj = new Date(date)
    return dateObj instanceof Date && !isNaN(dateObj)
  }

  static sanitizeText (text) {
    if (typeof text !== 'string') return ''

    return text
      .replace(/[<>]/g, '') // Remove potential HTML tags
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim()
  }

  static validateProfileName (name) {
    const errors = []

    if (!name || typeof name !== 'string') {
      errors.push('Name is required')
      return { isValid: false, errors }
    }

    const sanitized = this.sanitizeText(name)

    if (sanitized.length === 0) {
      errors.push('Name cannot be empty')
    }

    if (sanitized.length > 100) {
      errors.push('Name must be less than 100 characters')
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitized
    }
  }

  static validateProfileInfo (info) {
    const errors = []

    if (!info || typeof info !== 'string') {
      errors.push('Profile information is required')
      return { isValid: false, errors }
    }

    const sanitized = this.sanitizeText(info)

    if (sanitized.length === 0) {
      errors.push('Profile information cannot be empty')
    }

    if (sanitized.length > 2000) {
      errors.push('Profile information must be less than 2000 characters')
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitized
    }
  }

  static validateApiKey (apiKey) {
    const errors = []

    if (!apiKey || typeof apiKey !== 'string') {
      errors.push('API key is required')
      return { isValid: false, errors }
    }

    const trimmed = apiKey.trim()

    if (trimmed.length === 0) {
      errors.push('API key cannot be empty')
    }

    if (trimmed.length < 10) {
      errors.push('API key appears to be too short')
    }

    return {
      isValid: errors.length === 0,
      errors,
      sanitized: trimmed
    }
  }

  static validateTemperature (temperature) {
    const errors = []
    const num = parseFloat(temperature)

    if (isNaN(num)) {
      errors.push('Temperature must be a number')
    } else if (num < 0 || num > 2) {
      errors.push('Temperature must be between 0 and 2')
    }

    return {
      isValid: errors.length === 0,
      errors,
      value: num
    }
  }

  static validateMaxTokens (tokens) {
    const errors = []
    const num = parseInt(tokens)

    if (isNaN(num)) {
      errors.push('Max tokens must be a number')
    } else if (num < 1 || num > 64000) {
      errors.push('Max tokens must be between 1 and 64000')
    }

    return {
      isValid: errors.length === 0,
      errors,
      value: num
    }
  }
}
