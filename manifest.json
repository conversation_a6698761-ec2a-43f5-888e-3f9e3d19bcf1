// manifest.json
// Chrome Extension Manifest V3 configuration file
// Defines extension permissions, scripts, and metadata for Smart Autofill Assistant
{
  "manifest_version": 3,
  "name": "Smart Autofill Assistant",
  "version": "1.0.0",
  "description": "Intelligent form autofill assistant powered by AI semantic understanding",
  
  "permissions": [
    "storage",
    "activeTab",
    "scripting",
    "tabs"
  ],
  
  "host_permissions": [
    "https://generativelanguage.googleapis.com/*"
  ],
  
  "background": {
    "service_worker": "src/background/background.js"
  },
  
  "content_scripts": [
    {
      "matches": ["<all_urls>"],
      "js": ["src/content/content-main.js"],
      "run_at": "document_end"
    }
  ],
  
  "action": {
    "default_popup": "src/popup/popup.html",
    "default_title": "Smart Autofill Assistant",
    "default_icon": {
      "16": "src/assets/icons/icon-16.png",
      "32": "src/assets/icons/icon-32.png",
      "48": "src/assets/icons/icon-48.png",
      "128": "src/assets/icons/icon-128.png"
    }
  },
  
  "icons": {
    "16": "src/assets/icons/icon-16.png",
    "32": "src/assets/icons/icon-32.png",
    "48": "src/assets/icons/icon-48.png",
    "128": "src/assets/icons/icon-128.png"
  },
  
  "web_accessible_resources": [
    {
      "resources": ["src/shared/styles/*.css"],
      "matches": ["<all_urls>"]
    }
  ]
}
